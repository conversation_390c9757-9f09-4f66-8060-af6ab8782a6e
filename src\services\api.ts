import { Employee, ApiEmployee, ProgressItem } from '../types/dashboard';

const API_URL = import.meta.env.VITE_API_URL;

if (!API_URL) {
  throw new Error('VITE_API_URL environment variable is not defined');
}

// Function to generate initials from name
const generateInitials = (name: string): string => {
  const names = name.split(' ');
  if (names.length === 1) return names[0].charAt(0).toUpperCase();
  return `${names[0].charAt(0)}${names[names.length - 1].charAt(0)}`.toUpperCase();
};

// Function to generate consistent color from string
const generateAvatarColor = (str: string): string => {
  const colors = [
    '#af52de', '#5ac8fa', '#ff9500', '#34c759', '#ff3b30', '#007aff',
    '#5856d6', '#ff2d55', '#8e8e93', '#ffcc00', '#4cd964', '#ff3b30'
  ];
  
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  return colors[Math.abs(hash) % colors.length];
};

// Transform API response to Employee format
export const transformApiEmployee = (apiEmployee: ApiEmployee): Employee => {
  return {
    id: apiEmployee.email, // Use email as unique ID
    name: apiEmployee.name,
    email: apiEmployee.email,
    initials: generateInitials(apiEmployee.name),
    avatarColor: generateAvatarColor(apiEmployee.email),
    dcoins: apiEmployee.dcoins,
    achievements: apiEmployee.achievements
  };
};

/**
 * Fetch journey progression data for "Progresso na Jornada"
 */
export const fetchJourneyProgression = async (): Promise<ProgressItem[]> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      throw new Error('No access token found. Please login first.');
    }

    const url = `${API_URL}/v2/users/hr/journey-progression`;
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    // Map API response to ProgressItem[]
    return data.map((item: { name: string; percentage: number }, idx: number) => ({
      label: item.name,
      percentage: item.percentage,
      color: generateAvatarColor(item.name + idx),
    }));
  } catch (error) {
    console.error('Error fetching journey progression:', error);
    throw new Error('Failed to fetch journey progression data');
  }
};

/**
 * Fetch dreams progression data for "Sonhos: Cadastrados vs Realizados"
 */
export const fetchDreamsProgression = async (): Promise<{
  registered: number;
  achieved: number;
  percentage: number;
}> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      throw new Error('No access token found. Please login first.');
    }

    const url = `${API_URL}/v2/users/hr/dreams-progression`;
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      registered: data.registered,
      achieved: data.completed,
      percentage: data.progress,
    };
  } catch (error) {
    console.error('Error fetching dreams progression:', error);
    throw new Error('Failed to fetch dreams progression data');
  }
};

/**
 * Fetch financial profile distribution for "Distribuição por Perfil Financeiro"
 */
export const fetchFinancialProfileDistribution = async (): Promise<{ label: string; value: number }[]> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      throw new Error('No access token found. Please login first.');
    }

    const url = `${API_URL}/v2/users/hr/financialprofile-distribution`;
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Map API keys to Portuguese labels
    const labelMap: Record<string, string> = {
      undefined: 'Não definido',
      overindebted: 'Super-endividados',
      indebted: 'Endividados',
      balanced: 'Equilibrados',
      investor: 'Investidores'
    };

    // Convert API response to BarChartData[]
    return Object.entries(data).map(([key, value]) => ({
      label: labelMap[key] || key,
      value: Number(value)
    }));
  } catch (error) {
    console.error('Error fetching financial profile distribution:', error);
    throw new Error('Failed to fetch financial profile distribution');
  }
};

// Fetch top engagers with optional limit
export const fetchTopEngagers = async (limit?: number): Promise<Employee[]> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      throw new Error('No access token found. Please login first.');
    }

    const url = limit 
      ? `${API_URL}/v2/users/hr/top-engagers?limit=${limit}`
      : `${API_URL}/v2/users/hr/top-engagers`;
    
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    return data.map(transformApiEmployee);
  } catch (error) {
    console.error('Error fetching top engagers:', error);
    throw new Error('Failed to fetch top engagers data');
  }
};
