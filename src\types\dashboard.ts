export interface Employee {
  id: string;
  name: string;
  email: string;
  initials: string;
  avatarColor: string;
  dcoins: number;
  achievements: number;
}

export interface ApiEmployee {
  name: string;
  email: string;
  dcoins: number;
  achievements: number;
}

export interface ProgressItem {
  label: string;
  percentage: number;
  color: string;
}

export interface BarChartData {
  label: string;
  value: number;
  color?: string;
}

export interface HorizontalBarData {
  label: string;
  value: number;
  percentage: number;
}

export interface InsightData {
  icon: string;
  value: string;
  label: string;
  color: string;
}

export interface DreamsData {
  percentage: number;
  registered: number;
  achieved: number;
}
