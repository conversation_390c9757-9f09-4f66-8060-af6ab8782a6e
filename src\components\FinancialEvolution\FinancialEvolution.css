/* FinancialEvolution Component Styles */

.line-chart-container {
  position: relative;
  width: 100%;
  height: 250px;
}

.line-chart-legend {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
}

.legend-color-box {
  width: 12px;
  height: 12px;
  border-radius: 3px;
}

/* --- RESPONSIVE STYLES --- */
@media (max-width: 768px) {
  .line-chart-legend {
    flex-direction: column;
    gap: 1rem;
  }
}
