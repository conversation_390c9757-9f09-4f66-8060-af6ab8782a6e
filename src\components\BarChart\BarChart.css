/* BarChart Component Styles */

/* This is the key to making the bars have a proper height */
.bar-chart-container {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  height: 200px;
  width: 100%;
  gap: 8px;
  flex-grow: 1;
  padding-bottom: 40px;
  position: relative;
}

.bar-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  text-align: center;
  flex: 1;
  height: 100%;
  position: relative;
}

.bar {
  width: 100%;
  /* FIX 1: Increased width to prevent label wrapping */
  max-width: 65px; 
  /* BONUS: Style to match the image (flat top, rounded bottom) */
  border-radius: 6px 6px 0 0;
  transition: height 0.5s ease-out;
  min-height: 8px;
}

.bar:hover {
  filter: brightness(1.2); /* Make the bar slightly brighter on hover */
}

.bar-value {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 8px;
  color: #ffffff;
}

.bar-label {
  font-size: 0.75rem;
  color: #a0a0b0;
  margin-top: 10px;
  line-height: 1.2;
  text-align: center;
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  width: 90px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #a0a0b0;
}
