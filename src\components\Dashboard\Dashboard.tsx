import React, { useState, useEffect } from 'react';
import { LogOut } from 'lucide-react';
import DashboardHeader from '../DashboardHeader/DashboardHeader';
import TopEmployees from '../TopEmployees/TopEmployees';
import JourneyProgress from '../JourneyProgress/JourneyProgress';
import Dreams from '../Dreams/Dreams';
import Bar<PERSON>hart from '../BarChart/BarChart';
import HorizontalBarC<PERSON> from '../HorizontalBarChart/HorizontalBarChart';
import FinancialEvolution from '../FinancialEvolution/FinancialEvolution';
import Interests from '../Interests/Interests';
import AdditionalInsights from '../AdditionalInsights/AdditionalInsights';
import { fetchTopEngagers, fetchJourneyProgression, fetchDreamsProgression } from '../../services/api';
import './Dashboard.css';
import {
  financialProfileData as staticFinancialProfileData,
  ageRangeData,
  financialSituationData,
  financialObjectivesData,
  additionalInsights
} from '../../data/dashboardData';
import { fetchFinancialProfileDistribution } from '../../services/api';
import { ProgressItem } from '../../types/dashboard';

interface DashboardProps {
  onLogout?: () => void;
}

interface DashboardState {
  topEmployees: any[];
  allEmployees: any[];
  loading: boolean;
  error: string | null;
  journeyProgress: ProgressItem[] | null;
  journeyLoading: boolean;
  journeyError: string | null;
  dreamsData: {
    registered: number;
    achieved: number;
    percentage: number;
  } | null;
  dreamsLoading: boolean;
  dreamsError: string | null;
  financialProfileData: { label: string; value: number }[];
  financialProfileLoading: boolean;
  financialProfileError: string | null;
}

const Dashboard: React.FC<DashboardProps> = ({ onLogout }) => {
  const [state, setState] = useState<DashboardState>({
    topEmployees: [],
    allEmployees: [],
    loading: true,
    error: null,
    journeyProgress: null,
    journeyLoading: true,
    journeyError: null,
    dreamsData: null,
    dreamsLoading: true,
    dreamsError: null,
    financialProfileData: [],
    financialProfileLoading: true,
    financialProfileError: null
  });

  useEffect(() => {
    const fetchEmployeesData = async () => {
      try {
        setState(prev => ({ ...prev, loading: true, error: null }));
        
        // Fetch top 3 employees for the main display
        const topEngagers = await fetchTopEngagers(5);
        
        // Fetch all employees for the modal
        const allEngagers = await fetchTopEngagers();
        
        setState(prev => ({
          ...prev,
          topEmployees: topEngagers,
          allEmployees: allEngagers,
          loading: false,
          error: null
        }));
      } catch (error) {
        console.error('Error fetching employees data:', error);
        setState(prev => ({
          ...prev,
          loading: false,
          error: 'Failed to load employees data'
        }));
      }
    };

    const fetchJourneyData = async () => {
      try {
        setState(prev => ({ ...prev, journeyLoading: true, journeyError: null }));
        const progress = await fetchJourneyProgression();
        setState(prev => ({
          ...prev,
          journeyProgress: progress,
          journeyLoading: false,
          journeyError: null
        }));
      } catch (error) {
        console.error('Error fetching journey progression:', error);
        setState(prev => ({
          ...prev,
          journeyLoading: false,
          journeyError: 'Failed to load journey progression'
        }));
      }
    };

    const fetchDreamsData = async () => {
      try {
        setState(prev => ({ ...prev, dreamsLoading: true, dreamsError: null }));
        const dreams = await fetchDreamsProgression();
        setState(prev => ({
          ...prev,
          dreamsData: dreams,
          dreamsLoading: false,
          dreamsError: null
        }));
      } catch (error) {
        console.error('Error fetching dreams progression:', error);
        setState(prev => ({
          ...prev,
          dreamsLoading: false,
          dreamsError: 'Failed to load dreams progression'
        }));
      }
    };

    fetchEmployeesData();
    fetchJourneyData();
    fetchDreamsData();

    // Fetch financial profile distribution
    const fetchFinancialProfile = async () => {
      try {
        setState(prev => ({
          ...prev,
          financialProfileLoading: true,
          financialProfileError: null
        }));
        const data = await fetchFinancialProfileDistribution();
        setState(prev => ({
          ...prev,
          financialProfileData: data,
          financialProfileLoading: false,
          financialProfileError: null
        }));
      } catch (error) {
        setState(prev => ({
          ...prev,
          financialProfileData: staticFinancialProfileData,
          financialProfileLoading: false,
          financialProfileError: 'Erro ao carregar distribuição por perfil financeiro. Usando dados estáticos.'
        }));
      }
    };
    fetchFinancialProfile();
  }, []);
  return (
    <div className="dashboard">
      {onLogout && (
        <button onClick={onLogout} className="logout-button">
          <LogOut size={20} />
          Sair
        </button>
      )}
      
      <DashboardHeader />
      
      <main className="dashboard-grid">
        {state.loading ? (
          <div className="card card-top-employees">
            <div className="card-header">
              <h2 className="card-title">Top Colaboradores Engajados</h2>
            </div>
            <div className="loading-state">Carregando...</div>
          </div>
        ) : state.error ? (
          <div className="card card-top-employees">
            <div className="card-header">
              <h2 className="card-title">Top Colaboradores Engajados</h2>
            </div>
            <div className="error-state">{state.error}</div>
          </div>
        ) : (
          <TopEmployees employees={state.topEmployees} allEmployees={state.allEmployees} />
        )}

        {state.journeyLoading ? (
          <div className="card card-journey-progress">
            <h2 className="card-title">Progresso na Jornada</h2>
            <div className="loading-state">Carregando...</div>
          </div>
        ) : state.journeyError ? (
          <div className="card card-journey-progress">
            <h2 className="card-title">Progresso na Jornada</h2>
            <div className="error-state">{state.journeyError}</div>
          </div>
        ) : (
          <JourneyProgress progressData={state.journeyProgress || []} />
        )}

        {state.dreamsLoading ? (
          <div className="card card-dreams">
            <h2 className="card-title">
              Sonhos:<br />
              Cadastrados vs Realizados
            </h2>
            <div className="loading-state">Carregando...</div>
          </div>
        ) : state.dreamsError ? (
          <div className="card card-dreams">
            <h2 className="card-title">
              Sonhos:<br />
              Cadastrados vs Realizados
            </h2>
            <div className="error-state">{state.dreamsError}</div>
          </div>
        ) : state.dreamsData ? (
          <Dreams data={state.dreamsData} />
        ) : null}
        
        {state.financialProfileLoading ? (
          <div className="card card-financial-profile">
            <h2 className="card-title">Distribuição por Perfil Financeiro</h2>
            <div className="loading-state">Carregando...</div>
          </div>
        ) : state.financialProfileError ? (
          <div className="card card-financial-profile">
            <h2 className="card-title">Distribuição por Perfil Financeiro</h2>
            <div className="error-state">{state.financialProfileError}</div>
            <BarChart
              data={state.financialProfileData}
              title="Distribuição por Perfil Financeiro"
              className="card-financial-profile"
            />
          </div>
        ) : (
          <BarChart
            data={state.financialProfileData}
            title="Distribuição por Perfil Financeiro"
            className="card-financial-profile"
          />
        )}
        
        <BarChart 
          data={ageRangeData}
          title="Faixa Etária dos Colaboradores"
          className="card-age-range"
        />
        
        <HorizontalBarChart
          data={financialSituationData}
          title="Situação Financeira Declarada"
          className="card-financial-situation"
          barColor="#ff9500"
        />
        
        <FinancialEvolution />
        
        <Interests />
        
        <HorizontalBarChart
          data={financialObjectivesData}
          title="Principais Objetivos Financeiros"
          className="card-financial-objectives"
          barColor="#5ac8fa"
        />
        
        <AdditionalInsights insights={additionalInsights} />
      </main>
    </div>
  );
};

export default Dashboard;
