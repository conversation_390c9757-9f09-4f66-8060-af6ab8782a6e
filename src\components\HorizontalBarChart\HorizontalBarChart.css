/* HorizontalBarChart Component Styles */

.horizontal-bar-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.horizontal-bar-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.horizontal-bar-label-container {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: var(--color-text-secondary);
}

.horizontal-progress-bar {
  height: 12px;
  width: 100%;
  background-color: var(--color-border);
  border-radius: 6px;
  overflow: hidden;
}

.horizontal-progress-bar-fill {
  height: 100%;
  border-radius: 6px;
  background-color: var(--color-orange);
  transition: width 0.3s ease-out;
}
